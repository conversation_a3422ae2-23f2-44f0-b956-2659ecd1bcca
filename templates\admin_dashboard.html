<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - CBK</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin_dashboard.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/all.min.css') }} ">
</head>
<body>
    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    {% include 'dashboard_components/admin_sidebar.html' %}

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Navigation -->
        <div class="top-nav">
            <div class="nav-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title" id="pageTitle">Dashboard Overview</h1>
            </div>

            <div class="nav-right">
                <div class="search-box">
                    <input type="text" placeholder="Search data, reports, users...">
                    <i class="fas fa-search"></i>
                </div>

                <div class="user-menu">
                    <div class="user-avatar">A</div>
                    <span>Admin</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </div>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Total Records</span>
                        <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-database"></i>
                        </div>
                    </div>
                    <div class="stat-value">12,847</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i> +12.5% from last month
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Active Users</span>
                        <div class="stat-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="stat-value">2,341</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i> +8.2% from last week
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Data Processing</span>
                        <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                            <i class="fas fa-cogs"></i>
                        </div>
                    </div>
                    <div class="stat-value">98.7%</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i> +2.1% efficiency
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Revenue</span>
                        <div class="stat-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="stat-value">$45,892</div>
                    <div class="stat-change negative">
                        <i class="fas fa-arrow-down"></i> -3.2% from last month
                    </div>
                </div>
            </div>

            <!-- Charts Grid -->
            <div class="chart-grid">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Data Trends</h3>
                        <div>
                            <select style="padding: 5px 10px; border: 1px solid #e1e5e9; border-radius: 4px; font-family: poppins;">
                                <option>Last 7 days</option>
                                <option>Last 30 days</option>
                                <option>Last 3 months</option>
                                <option>Last year</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-placeholder">
                        <i class="fas fa-chart-line" style="margin-right: 10px;"></i>
                        Chart will be rendered here
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Quick Stats</h3>
                    </div>
                    <div class="chart-placeholder" style="height: 300px;">
                        <i class="fas fa-chart-pie" style="margin-right: 10px;"></i>
                        Pie chart placeholder
                    </div>
                </div>
            </div>

            <!-- Additional Chart Row -->
            <div class="chart-grid" style="grid-template-columns: 1fr 1fr;">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Performance Metrics</h3>
                    </div>
                    <div class="chart-placeholder">
                        <i class="fas fa-chart-bar" style="margin-right: 10px;"></i>
                        Bar chart placeholder
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Geographic Distribution</h3>
                    </div>
                    <div class="chart-placeholder">
                        <i class="fas fa-map" style="margin-right: 10px;"></i>
                        Map visualization placeholder
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dashboard JavaScript functionality
        document.addEventListener('DOMContentLoaded', function() {
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const menuItems = document.querySelectorAll('.menu-item');
            const pageTitle = document.getElementById('pageTitle');

            // Mobile menu toggle
            menuToggle.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    sidebar.classList.toggle('mobile-open');
                    sidebarOverlay.classList.toggle('active');
                } else {
                    sidebar.classList.toggle('collapsed');
                    mainContent.classList.toggle('expanded');
                }
            });

            // Close sidebar when clicking overlay (mobile)
            sidebarOverlay.addEventListener('click', function() {
                sidebar.classList.remove('mobile-open');
                sidebarOverlay.classList.remove('active');
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    sidebar.classList.remove('mobile-open');
                    sidebarOverlay.classList.remove('active');
                }
            });

            // Menu item navigation
            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    if (this.getAttribute('href') === '#') {
                        e.preventDefault();

                        // Remove active class from all items
                        menuItems.forEach(menuItem => {
                            menuItem.classList.remove('active');
                        });

                        // Add active class to clicked item
                        this.classList.add('active');

                        // Update page title
                        const pageName = this.getAttribute('data-page');
                        updatePageContent(pageName);

                        // Close mobile menu if open
                        if (window.innerWidth <= 768) {
                            sidebar.classList.remove('mobile-open');
                            sidebarOverlay.classList.remove('active');
                        }
                    }
                });
            });

            // Update page content based on navigation
            function updatePageContent(page) {
                const titles = {
                    'dashboard': 'Dashboard Overview',
                    'analytics': 'Analytics & Insights',
                    'charts': 'Charts & Reports',
                    'data': 'Data Management',
                    'users': 'User Management',
                    'settings': 'Settings',
                    'export': 'Export Data'
                };

                pageTitle.textContent = titles[page] || 'Dashboard';

                // Here you can add logic to show/hide different content sections
                // For now, we'll just update the title
                console.log('Navigated to:', page);
            }

            // Simulate real-time data updates
            function updateStats() {
                const statValues = document.querySelectorAll('.stat-value');
                statValues.forEach(stat => {
                    const currentValue = parseInt(stat.textContent.replace(/[^0-9]/g, ''));
                    if (currentValue) {
                        const variation = Math.floor(Math.random() * 10) - 5; // Random variation
                        const newValue = Math.max(0, currentValue + variation);

                        if (stat.textContent.includes('$')) {
                            stat.textContent = '$' + newValue.toLocaleString();
                        } else if (stat.textContent.includes('%')) {
                            stat.textContent = newValue + '%';
                        } else {
                            stat.textContent = newValue.toLocaleString();
                        }
                    }
                });
            }

            // Update stats every 30 seconds (optional - remove if not needed)
            // setInterval(updateStats, 30000);

            // Search functionality
            const searchInput = document.querySelector('.search-box input');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        const searchTerm = this.value.trim();
                        if (searchTerm) {
                            console.log('Searching for:', searchTerm);
                            // Implement search functionality here
                            alert('Search functionality will be implemented here for: ' + searchTerm);
                        }
                    }
                });
            }

            // User menu functionality
            const userMenu = document.querySelector('.user-menu');
            if (userMenu) {
                userMenu.addEventListener('click', function() {
                    // Implement user menu dropdown here
                    console.log('User menu clicked');
                    alert('User menu options:\n- Profile Settings\n- Account Info\n- Logout');
                });
            }

            // Chart placeholder click handlers (for future chart integration)
            const chartPlaceholders = document.querySelectorAll('.chart-placeholder');
            chartPlaceholders.forEach(placeholder => {
                placeholder.addEventListener('click', function() {
                    console.log('Chart clicked - ready for integration');
                    // This is where you'll integrate your actual charts
                });
            });

            // Add smooth scrolling for better UX
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }go
                    
                });
            });
        });

        // Utility function to format numbers
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        // Function to add new chart (placeholder for future use)
        function addChart(containerId, chartType, data) {
            console.log(`Adding ${chartType} chart to ${containerId}`, data);
            // This function will be used when integrating actual charting libraries
        }
    </script>
</body>
</html>