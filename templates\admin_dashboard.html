<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Admin Dashboard - CBK</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin_dashboard.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/all.min.css') }} ">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-gauge@0.3.0/dist/chartjs-gauge.min.js"></script>
</head>
<body>
    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    {% include 'dashboard_components/admin_sidebar.html' %}

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Navigation -->
        <div class="top-nav">
            <div class="nav-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title" id="pageTitle">Dashboard Overview</h1>
            </div>

            <div class="nav-right">
                <div class="search-box">
                    <input type="text" placeholder="Search data, reports, users...">
                    <i class="fas fa-search"></i>
                </div>

                <div class="user-menu">
                    <div class="user-avatar">A</div>
                    <span>Admin</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </div>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Critical Status Bar -->
            <div class="status-bar">
                <div class="status-item status-optimal">
                    <i class="fas fa-check-circle"></i>
                    <span>System Operational</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-clock"></i>
                    <span id="currentTime">--:--:--</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-thermometer-half"></i>
                    <span>Water: <span id="waterTemp">--</span>°C</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-bolt"></i>
                    <span>Power: <span id="totalMW">--</span> MW</span>
                </div>
            </div>

            <!-- Power Generation Section -->
            <div class="section-header">
                <h2><i class="fas fa-bolt"></i> Power Generation & Electrical</h2>
            </div>
            <div class="power-grid">
                <div class="gauge-card">
                    <div class="gauge-header">
                        <h3>Total Power</h3>
                        <span class="unit">MW</span>
                    </div>
                    <div class="gauge-container">
                        <canvas id="totalMWGauge" width="200" height="200"></canvas>
                        <div class="gauge-value" id="totalMWValue">0.0</div>
                    </div>
                </div>

                <div class="gauge-card">
                    <div class="gauge-header">
                        <h3>Turbine Output</h3>
                        <span class="unit">MW</span>
                    </div>
                    <div class="gauge-container">
                        <canvas id="turbineMWGauge" width="200" height="200"></canvas>
                        <div class="gauge-value" id="turbineMWValue">0.0</div>
                    </div>
                </div>

                <div class="gauge-card">
                    <div class="gauge-header">
                        <h3>Pump Consumption</h3>
                        <span class="unit">MW</span>
                    </div>
                    <div class="gauge-container">
                        <canvas id="pumpMWGauge" width="200" height="200"></canvas>
                        <div class="gauge-value" id="pumpMWValue">0.0</div>
                    </div>
                </div>

                <div class="gauge-card">
                    <div class="gauge-header">
                        <h3>Voltage</h3>
                        <span class="unit">V</span>
                    </div>
                    <div class="gauge-container">
                        <canvas id="voltageGauge" width="200" height="200"></canvas>
                        <div class="gauge-value" id="voltageValue">0.0</div>
                    </div>
                </div>

                <div class="gauge-card">
                    <div class="gauge-header">
                        <h3>Current</h3>
                        <span class="unit">A</span>
                    </div>
                    <div class="gauge-container">
                        <canvas id="currentGauge" width="200" height="200"></canvas>
                        <div class="gauge-value" id="currentValue">0.0</div>
                    </div>
                </div>

                <div class="gauge-card">
                    <div class="gauge-header">
                        <h3>Frequency</h3>
                        <span class="unit">Hz</span>
                    </div>
                    <div class="gauge-container">
                        <canvas id="frequencyGauge" width="200" height="200"></canvas>
                        <div class="gauge-value" id="frequencyValue">0.0</div>
                    </div>
                </div>
            </div>

            <!-- Water Management Section -->
            <div class="section-header">
                <h2><i class="fas fa-tint"></i> Water Management & Flow</h2>
            </div>
            <div class="water-grid">
                <div class="water-level-card">
                    <div class="level-header">
                        <h3>Upper Reservoir</h3>
                        <div class="sensor-status">
                            <span class="sensor-dot active"></span>
                            <span class="sensor-dot active"></span>
                        </div>
                    </div>
                    <div class="tank-container">
                        <div class="tank-visual">
                            <div class="water-level" id="upperWaterLevel" style="height: 65%;"></div>
                            <div class="tank-readings">
                                <span>S1: <span id="upperWater1">--</span>m</span>
                                <span>S2: <span id="upperWater2">--</span>m</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="water-level-card">
                    <div class="level-header">
                        <h3>Lower Reservoir</h3>
                        <div class="sensor-status">
                            <span class="sensor-dot active"></span>
                            <span class="sensor-dot active"></span>
                        </div>
                    </div>
                    <div class="tank-container">
                        <div class="tank-visual">
                            <div class="water-level" id="lowerWaterLevel" style="height: 45%;"></div>
                            <div class="tank-readings">
                                <span>S1: <span id="lowerWater1">--</span>m</span>
                                <span>S2: <span id="lowerWater2">--</span>m</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flow-card">
                    <div class="flow-header">
                        <h3>Flow Rates</h3>
                    </div>
                    <div class="flow-meters">
                        <div class="flow-meter">
                            <div class="flow-icon">
                                <i class="fas fa-cog fa-spin"></i>
                            </div>
                            <div class="flow-info">
                                <span class="flow-label">Turbine</span>
                                <span class="flow-value" id="flowTurbine">-- m³/s</span>
                            </div>
                        </div>
                        <div class="flow-meter">
                            <div class="flow-icon">
                                <i class="fas fa-pump-medical"></i>
                            </div>
                            <div class="flow-info">
                                <span class="flow-label">Pump</span>
                                <span class="flow-value" id="flowPump">-- m³/s</span>
                            </div>
                        </div>
                        <div class="flow-meter">
                            <div class="flow-icon">
                                <i class="fas fa-water"></i>
                            </div>
                            <div class="flow-info">
                                <span class="flow-label">Other</span>
                                <span class="flow-value" id="flowOther">-- m³/s</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="temp-card">
                    <div class="temp-header">
                        <h3>Water Temperature</h3>
                    </div>
                    <div class="thermometer-container">
                        <div class="thermometer">
                            <div class="thermometer-bulb"></div>
                            <div class="thermometer-tube">
                                <div class="mercury" id="waterTempMercury" style="height: 60%;"></div>
                            </div>
                            <div class="temp-scale">
                                <span>30°</span>
                                <span>20°</span>
                                <span>10°</span>
                                <span>0°</span>
                            </div>
                        </div>
                        <div class="temp-reading" id="waterTempReading">22.5°C</div>
                    </div>
                </div>
            </div>

            <!-- Environmental Monitoring Section -->
            <div class="section-header">
                <h2><i class="fas fa-cloud-sun"></i> Environmental Monitoring</h2>
            </div>
            <div class="environmental-grid">
                <div class="weather-card">
                    <div class="weather-header">
                        <h3>Weather Station</h3>
                        <div class="weather-status">
                            <i class="fas fa-sun"></i>
                            <span>Clear</span>
                        </div>
                    </div>
                    <div class="weather-metrics">
                        <div class="metric">
                            <i class="fas fa-thermometer-half"></i>
                            <span>Air Temp</span>
                            <strong id="airTemp">--°C</strong>
                        </div>
                        <div class="metric">
                            <i class="fas fa-tint"></i>
                            <span>Humidity</span>
                            <strong id="humidity">--%</strong>
                        </div>
                        <div class="metric">
                            <i class="fas fa-cloud-rain"></i>
                            <span>Rainfall</span>
                            <strong id="rainfall">-- mm</strong>
                        </div>
                        <div class="metric">
                            <i class="fas fa-compress-arrows-alt"></i>
                            <span>Pressure</span>
                            <strong id="pressure">-- hPa</strong>
                        </div>
                    </div>
                </div>

                <div class="wind-card">
                    <div class="wind-header">
                        <h3>Wind Monitoring</h3>
                    </div>
                    <div class="wind-display">
                        <div class="compass">
                            <div class="compass-face">
                                <div class="compass-needle" id="windNeedle" style="transform: rotate(45deg);"></div>
                                <div class="compass-directions">
                                    <span class="north">N</span>
                                    <span class="east">E</span>
                                    <span class="south">S</span>
                                    <span class="west">W</span>
                                </div>
                            </div>
                        </div>
                        <div class="wind-data">
                            <div class="wind-speed">
                                <span>Speed</span>
                                <strong id="windSpeed">-- m/s</strong>
                            </div>
                            <div class="wind-direction">
                                <span>Direction</span>
                                <strong id="windDirection">--°</strong>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="solar-card">
                    <div class="solar-header">
                        <h3>Solar Radiation</h3>
                    </div>
                    <div class="solar-display">
                        <div class="solar-gauge">
                            <canvas id="solarGauge" width="150" height="150"></canvas>
                            <div class="solar-value" id="solarValue">-- W/m²</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dashboard JavaScript functionality
        document.addEventListener('DOMContentLoaded', function() {
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const menuItems = document.querySelectorAll('.menu-item');
            const pageTitle = document.getElementById('pageTitle');

            // Enhanced mobile menu toggle with touch support
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                if (window.innerWidth <= 768) {
                    sidebar.classList.toggle('mobile-open');
                    sidebarOverlay.classList.toggle('active');
                    // Prevent body scroll when sidebar is open
                    document.body.style.overflow = sidebar.classList.contains('mobile-open') ? 'hidden' : '';
                } else {
                    sidebar.classList.toggle('collapsed');
                    mainContent.classList.toggle('expanded');
                }
            });

            // Touch gesture support for sidebar
            let touchStartX = 0;
            let touchEndX = 0;

            document.addEventListener('touchstart', function(e) {
                touchStartX = e.changedTouches[0].screenX;
            });

            document.addEventListener('touchend', function(e) {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipeGesture();
            });

            function handleSwipeGesture() {
                const swipeThreshold = 50;
                const swipeDistance = touchEndX - touchStartX;

                if (window.innerWidth <= 768) {
                    // Swipe right to open sidebar
                    if (swipeDistance > swipeThreshold && touchStartX < 50) {
                        sidebar.classList.add('mobile-open');
                        sidebarOverlay.classList.add('active');
                        document.body.style.overflow = 'hidden';
                    }
                    // Swipe left to close sidebar
                    else if (swipeDistance < -swipeThreshold && sidebar.classList.contains('mobile-open')) {
                        sidebar.classList.remove('mobile-open');
                        sidebarOverlay.classList.remove('active');
                        document.body.style.overflow = '';
                    }
                }
            }

            // Enhanced overlay handling for mobile
            sidebarOverlay.addEventListener('click', function() {
                sidebar.classList.remove('mobile-open');
                sidebarOverlay.classList.remove('active');
                document.body.style.overflow = '';
            });

            // Enhanced window resize handling
            let resizeTimer;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(function() {
                    if (window.innerWidth > 768) {
                        sidebar.classList.remove('mobile-open');
                        sidebarOverlay.classList.remove('active');
                        document.body.style.overflow = '';

                        // Reset sidebar to normal state on desktop
                        if (sidebar.classList.contains('collapsed')) {
                            sidebar.classList.remove('collapsed');
                            mainContent.classList.remove('expanded');
                        }
                    }

                    // Recalculate gauge sizes on resize
                    if (typeof updateGaugeSizes === 'function') {
                        updateGaugeSizes();
                    }
                }, 250);
            });

            // Prevent zoom on double tap for iOS
            let lastTouchEnd = 0;
            document.addEventListener('touchend', function(event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);

            // Enhanced menu item navigation with touch feedback
            menuItems.forEach(item => {
                // Add touch feedback
                item.addEventListener('touchstart', function() {
                    this.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
                });

                item.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 150);
                });

                item.addEventListener('click', function(e) {
                    if (this.getAttribute('href') === '#') {
                        e.preventDefault();

                        // Remove active class from all items
                        menuItems.forEach(menuItem => {
                            menuItem.classList.remove('active');
                        });

                        // Add active class to clicked item
                        this.classList.add('active');

                        // Update page title
                        const pageName = this.getAttribute('data-page');
                        updatePageContent(pageName);

                        // Close mobile menu if open with smooth transition
                        if (window.innerWidth <= 768) {
                            setTimeout(() => {
                                sidebar.classList.remove('mobile-open');
                                sidebarOverlay.classList.remove('active');
                                document.body.style.overflow = '';
                            }, 100);
                        }
                    }
                });
            });

            // Update page content based on navigation
            function updatePageContent(page) {
                const titles = {
                    'dashboard': 'Dashboard Overview',
                    'analytics': 'Analytics & Insights',
                    'charts': 'Charts & Reports',
                    'data': 'Data Management',
                    'users': 'User Management',
                    'settings': 'Settings',
                    'export': 'Export Data'
                };

                pageTitle.textContent = titles[page] || 'Dashboard';

                // Here you can add logic to show/hide different content sections
                // For now, we'll just update the title
                console.log('Navigated to:', page);
            }

            // Simulate real-time data updates
            function updateStats() {
                const statValues = document.querySelectorAll('.stat-value');
                statValues.forEach(stat => {
                    const currentValue = parseInt(stat.textContent.replace(/[^0-9]/g, ''));
                    if (currentValue) {
                        const variation = Math.floor(Math.random() * 10) - 5; // Random variation
                        const newValue = Math.max(0, currentValue + variation);

                        if (stat.textContent.includes('$')) {
                            stat.textContent = '$' + newValue.toLocaleString();
                        } else if (stat.textContent.includes('%')) {
                            stat.textContent = newValue + '%';
                        } else {
                            stat.textContent = newValue.toLocaleString();
                        }
                    }
                });
            }

            // Update stats every 30 seconds (optional - remove if not needed)
            // setInterval(updateStats, 30000);

            // Search functionality
            const searchInput = document.querySelector('.search-box input');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        const searchTerm = this.value.trim();
                        if (searchTerm) {
                            console.log('Searching for:', searchTerm);
                            // Implement search functionality here
                            alert('Search functionality will be implemented here for: ' + searchTerm);
                        }
                    }
                });
            }

            // User menu functionality
            const userMenu = document.querySelector('.user-menu');
            if (userMenu) {
                userMenu.addEventListener('click', function() {
                    // Implement user menu dropdown here
                    console.log('User menu clicked');
                    alert('User menu options:\n- Profile Settings\n- Account Info\n- Logout');
                });
            }

            // Chart placeholder click handlers (for future chart integration)
            const chartPlaceholders = document.querySelectorAll('.chart-placeholder');
            chartPlaceholders.forEach(placeholder => {
                placeholder.addEventListener('click', function() {
                    console.log('Chart clicked - ready for integration');
                    // This is where you'll integrate your actual charts
                });
            });

            // Add smooth scrolling for better UX
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Initialize sensor dashboard with mobile optimization
            initializeGauges();
            updateSensorDisplays();

            // Start real-time updates with adaptive intervals
            const updateInterval = window.innerWidth <= 480 ? 8000 : 5000; // Slower on mobile to save battery
            setInterval(fetchRealTimeData, updateInterval);

            // Initial data fetch
            fetchRealTimeData();

            // Add mobile-specific event listeners
            if ('serviceWorker' in navigator) {
                // Register service worker for offline capability (optional)
                console.log('Service Worker support detected');
            }

            // Handle device orientation changes
            window.addEventListener('orientationchange', function() {
                setTimeout(() => {
                    updateGaugeSizes();
                    updateSensorDisplays();
                }, 500);
            });

            // Optimize for mobile performance
            if (window.innerWidth <= 768) {
                // Reduce animation complexity on mobile
                document.documentElement.style.setProperty('--animation-duration', '0.2s');

                // Add mobile-specific classes
                document.body.classList.add('mobile-device');
            }
        });

        // Utility function to format numbers
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        // Sensor Data Management
        let sensorData = {
            mw: 45.2,
            mw_turbine: 48.7,
            mw_pump: 3.5,
            mvar: 12.3,
            voltage: 13800,
            current: 1250,
            hz: 50.0,
            flow_turbine: 125.5,
            flow_pump: 15.2,
            flow_other: 8.7,
            net_head: 85.3,
            temp_water: 22.5,
            upper_water1: 65.2,
            upper_water2: 64.8,
            lower_water1: 45.1,
            lower_water2: 44.9,
            evaporation: 2.3,
            air_temp: 28.5,
            humidity: 65,
            rainfall: 0.0,
            pressure: 1013.2,
            windspeed: 12.5,
            wind_direction: 225,
            solar_radiation: 850
        };

        // Gauge configurations
        const gaugeConfigs = {
            totalMW: { min: 0, max: 100, color: '#667eea', unit: 'MW' },
            turbineMW: { min: 0, max: 80, color: '#10b981', unit: 'MW' },
            pumpMW: { min: 0, max: 20, color: '#f59e0b', unit: 'MW' },
            voltage: { min: 0, max: 20000, color: '#ef4444', unit: 'V' },
            current: { min: 0, max: 2000, color: '#8b5cf6', unit: 'A' },
            frequency: { min: 45, max: 55, color: '#06b6d4', unit: 'Hz' },
            solar: { min: 0, max: 1200, color: '#f59e0b', unit: 'W/m²' }
        };

        // Initialize gauges
        function initializeGauges() {
            Object.keys(gaugeConfigs).forEach(gaugeId => {
                const config = gaugeConfigs[gaugeId];
                const canvas = document.getElementById(gaugeId + 'Gauge');
                if (canvas) {
                    createGauge(canvas, config);
                }
            });
        }

        // Create responsive gauge chart
        function createGauge(canvas, config) {
            const ctx = canvas.getContext('2d');

            // Adjust canvas size based on screen size
            const isMobile = window.innerWidth <= 480;
            const isTablet = window.innerWidth <= 768;

            if (isMobile) {
                canvas.width = 120;
                canvas.height = 120;
            } else if (isTablet) {
                canvas.width = 150;
                canvas.height = 150;
            } else {
                canvas.width = 200;
                canvas.height = 200;
            }

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    datasets: [{
                        data: [0, config.max],
                        backgroundColor: [config.color, '#e5e7eb'],
                        borderWidth: 0,
                        cutout: '75%'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    rotation: -90,
                    circumference: 180,
                    plugins: {
                        legend: { display: false },
                        tooltip: { enabled: false }
                    },
                    animation: {
                        duration: isMobile ? 500 : 1000
                    }
                }
            });
        }

        // Update gauge sizes on window resize
        function updateGaugeSizes() {
            // Re-initialize gauges with new sizes
            setTimeout(() => {
                initializeGauges();
            }, 300);
        }

        // Update sensor displays
        function updateSensorDisplays() {
            // Update status bar
            document.getElementById('currentTime').textContent = new Date().toLocaleTimeString();
            document.getElementById('waterTemp').textContent = sensorData.temp_water.toFixed(1);
            document.getElementById('totalMW').textContent = sensorData.mw.toFixed(1);

            // Update gauge values
            document.getElementById('totalMWValue').textContent = sensorData.mw.toFixed(1);
            document.getElementById('turbineMWValue').textContent = sensorData.mw_turbine.toFixed(1);
            document.getElementById('pumpMWValue').textContent = sensorData.mw_pump.toFixed(1);
            document.getElementById('voltageValue').textContent = sensorData.voltage.toFixed(0);
            document.getElementById('currentValue').textContent = sensorData.current.toFixed(0);
            document.getElementById('frequencyValue').textContent = sensorData.hz.toFixed(1);

            // Update water levels
            const upperLevel = ((sensorData.upper_water1 + sensorData.upper_water2) / 2 / 100) * 100;
            const lowerLevel = ((sensorData.lower_water1 + sensorData.lower_water2) / 2 / 100) * 100;

            document.getElementById('upperWaterLevel').style.height = upperLevel + '%';
            document.getElementById('lowerWaterLevel').style.height = lowerLevel + '%';

            document.getElementById('upperWater1').textContent = sensorData.upper_water1.toFixed(1);
            document.getElementById('upperWater2').textContent = sensorData.upper_water2.toFixed(1);
            document.getElementById('lowerWater1').textContent = sensorData.lower_water1.toFixed(1);
            document.getElementById('lowerWater2').textContent = sensorData.lower_water2.toFixed(1);

            // Update flow rates
            document.getElementById('flowTurbine').textContent = sensorData.flow_turbine.toFixed(1) + ' m³/s';
            document.getElementById('flowPump').textContent = sensorData.flow_pump.toFixed(1) + ' m³/s';
            document.getElementById('flowOther').textContent = sensorData.flow_other.toFixed(1) + ' m³/s';

            // Update water temperature
            const tempPercent = (sensorData.temp_water / 40) * 100;
            document.getElementById('waterTempMercury').style.height = tempPercent + '%';
            document.getElementById('waterTempReading').textContent = sensorData.temp_water.toFixed(1) + '°C';

            // Update environmental data
            document.getElementById('airTemp').textContent = sensorData.air_temp.toFixed(1) + '°C';
            document.getElementById('humidity').textContent = sensorData.humidity.toFixed(0) + '%';
            document.getElementById('rainfall').textContent = sensorData.rainfall.toFixed(1) + ' mm';
            document.getElementById('pressure').textContent = sensorData.pressure.toFixed(1) + ' hPa';

            // Update wind data
            document.getElementById('windSpeed').textContent = sensorData.windspeed.toFixed(1) + ' m/s';
            document.getElementById('windDirection').textContent = sensorData.wind_direction.toFixed(0) + '°';
            document.getElementById('windNeedle').style.transform = `rotate(${sensorData.wind_direction}deg)`;

            // Update solar radiation
            document.getElementById('solarValue').textContent = sensorData.solar_radiation.toFixed(0) + ' W/m²';
        }

        // Enhanced data updates with mobile optimization
        function simulateDataUpdates() {
            // Add small random variations to simulate real sensor data
            Object.keys(sensorData).forEach(key => {
                const variation = (Math.random() - 0.5) * 0.1; // ±5% variation
                sensorData[key] = Math.max(0, sensorData[key] * (1 + variation));
            });

            updateSensorDisplays();

            // Update connection status indicator
            updateConnectionStatus(true);
        }

        // Connection status management
        function updateConnectionStatus(isConnected) {
            const statusItems = document.querySelectorAll('.status-item');
            if (statusItems.length > 0) {
                const firstStatus = statusItems[0];
                if (isConnected) {
                    firstStatus.className = 'status-item status-optimal';
                    firstStatus.innerHTML = '<i class="fas fa-check-circle"></i><span>System Online</span>';
                } else {
                    firstStatus.className = 'status-item status-error';
                    firstStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i><span>Connection Lost</span>';
                }
            }
        }

        // Mobile-optimized data fetching
        function fetchRealTimeData() {
            if (navigator.onLine) {
                fetch('/api/sensor-data')
                    .then(response => response.json())
                    .then(data => {
                        sensorData = data;
                        updateSensorDisplays();
                        updateConnectionStatus(true);
                    })
                    .catch(error => {
                        console.error('Data fetch error:', error);
                        updateConnectionStatus(false);
                        // Fall back to simulation
                        simulateDataUpdates();
                    });
            } else {
                updateConnectionStatus(false);
                simulateDataUpdates();
            }
        }

        // Network status monitoring
        window.addEventListener('online', function() {
            updateConnectionStatus(true);
            fetchRealTimeData();
        });

        window.addEventListener('offline', function() {
            updateConnectionStatus(false);
        });

        // Function to add new chart (placeholder for future use)
        function addChart(containerId, chartType, data) {
            console.log(`Adding ${chartType} chart to ${containerId}`, data);
            // This function will be used when integrating actual charting libraries
        }
    </script>
</body>
</html>