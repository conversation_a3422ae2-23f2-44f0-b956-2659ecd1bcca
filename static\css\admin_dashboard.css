
        :root {
            --animation-duration: 0.3s;
            --mobile-padding: 10px;
            --desktop-padding: 30px;
            --gauge-size-mobile: 120px;
            --gauge-size-tablet: 150px;
            --gauge-size-desktop: 200px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON><PERSON>, <PERSON>pins, Poppins, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Mobile-specific optimizations */
        body.mobile-device {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }

        body.mobile-device * {
            transition-duration: var(--animation-duration);
        }

        /* Status error class */
        .status-error {
            color: #ef4444;
        }

        /* Improved touch targets */
        .touch-target {
            min-width: 44px;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateX(0);
            overflow-x: hidden;
            transition: transform 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar.collapsed {
            transform: translateX(-280px);
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .sidebar-header h2 {
            font-size: 24px;
            font-weight: 300;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 14px;
            opacity: 0.8;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            display: block;
            padding: 15px 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .menu-item:hover,
        .menu-item.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: #fff;
            transform: translateX(5px);
        }

        .menu-item i {
            width: 20px;
            margin-right: 15px;
            text-align: center;
        }

        .menu-item span {
            font-size: 14px;
            font-weight: 500;
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            transition: margin-left 0.3s ease;
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* Top Navigation */
        .top-nav {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-left {
            display: flex;
            align-items: center;
        }

        .menu-toggle {
            background: none;
            border: none;
            font-size: 20px;
            color: #667eea;
            cursor: pointer;
            margin-right: 20px;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.3s ease;
        }

        .menu-toggle:hover {
            background-color: #f0f2ff;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            padding: 8px 40px 8px 15px;
            border: 1px solid #e1e5e9;
            border-radius: 20px;
            width: 250px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: #667eea;
        }

        .search-box i {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            padding: 8px 15px;
            border-radius: 20px;
            transition: background-color 0.3s ease;
        }

        .user-menu:hover {
            background-color: #f0f2ff;
        }

        .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        /* Dashboard Content */
        .dashboard-content {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stat-title {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-change {
            font-size: 12px;
            font-weight: 500;
        }

        .stat-change.positive {
            color: #10b981;
        }

        .stat-change.negative {
            color: #ef4444;
        }

        /* Chart Container */
        .chart-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e1e5e9;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, #f0f2ff 0%, #e6f3ff 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #667eea;
            font-size: 16px;
            font-weight: 500;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-280px);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .top-nav {
                padding: 15px 20px;
            }

            .search-box input {
                width: 200px;
            }

            .dashboard-content {
                padding: 20px;
            }

            .chart-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        @media (max-width: 480px) {
            .search-box {
                display: none;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Overlay for mobile */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }

        .sidebar-overlay.active {
            display: block;
        }

        /* Sensor Dashboard Styles */
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .status-item i {
            font-size: 16px;
        }

        .status-optimal {
            color: #10b981;
        }

        .section-header {
            margin: 30px 0 20px 0;
        }

        .section-header h2 {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-header i {
            color: #667eea;
        }

        /* Power Generation Grid */
        .power-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .gauge-card {
            background: linear-gradient(145deg, #374151 0%, #4b5563 100%);
            padding: 24px;
            border-radius: 16px;
            box-shadow:
                0 4px 20px rgba(0, 0, 0, 0.25),
                0 1px 4px rgba(0, 0, 0, 0.15);
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(75, 85, 99, 0.8);
        }

        .gauge-card:hover {
            transform: translateY(-8px);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.35),
                0 4px 16px rgba(0, 0, 0, 0.25);
        }

        .gauge-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .gauge-header h3 {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
        }

        .unit {
            font-size: 12px;
            color: #d1d5db;
            background: rgba(255, 255, 255, 0.1);
            padding: 2px 8px;
            border-radius: 12px;
        }

        .gauge-container {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .gauge-value {
            display: none; /* Hide HTML value since we're drawing it on canvas */
        }

        /* Water Management Grid */
        .water-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .water-level-card, .flow-card, .temp-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .level-header, .flow-header, .temp-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .level-header h3, .flow-header h3, .temp-header h3 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .sensor-status {
            display: flex;
            gap: 5px;
        }

        .sensor-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #e5e7eb;
        }

        .sensor-dot.active {
            background: #10b981;
            box-shadow: 0 0 5px rgba(16, 185, 129, 0.5);
        }

        /* Tank Visualization */
        .tank-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .tank-visual {
            position: relative;
            width: 80px;
            height: 150px;
            border: 3px solid #667eea;
            border-radius: 0 0 10px 10px;
            background: #f8f9fa;
            overflow: hidden;
        }

        .water-level {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%);
            transition: height 0.5s ease;
            border-radius: 0 0 7px 7px;
        }

        .tank-readings {
            position: absolute;
            right: -60px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 5px;
            font-size: 12px;
            color: #666;
        }

        /* Flow Meters */
        .flow-meters {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .flow-meter {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .flow-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .flow-info {
            display: flex;
            flex-direction: column;
        }

        .flow-label {
            font-size: 12px;
            color: #666;
        }

        .flow-value {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        /* Thermometer */
        .thermometer-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .thermometer {
            position: relative;
            display: flex;
            align-items: flex-end;
            gap: 10px;
            height: 150px;
        }

        .thermometer-bulb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ef4444;
            position: relative;
            z-index: 2;
        }

        .thermometer-tube {
            width: 8px;
            height: 120px;
            background: #e5e7eb;
            border-radius: 4px 4px 0 0;
            position: relative;
            overflow: hidden;
        }

        .mercury {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background: linear-gradient(180deg, #ef4444 0%, #dc2626 100%);
            transition: height 0.5s ease;
            border-radius: 0 0 4px 4px;
        }

        .temp-scale {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 120px;
            font-size: 10px;
            color: #666;
        }

        .temp-reading {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            text-align: center;
        }

        /* Environmental Grid */
        .environmental-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .weather-card, .wind-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .weather-header, .wind-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .weather-header h3, .wind-header h3 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .weather-status {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: #f59e0b;
        }

        .weather-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .metric {
            display: flex;
            flex-direction: column;
            gap: 5px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .metric i {
            color: #667eea;
            font-size: 16px;
        }

        .metric span {
            font-size: 12px;
            color: #666;
        }

        .metric strong {
            font-size: 16px;
            color: #333;
        }

        /* Wind Compass */
        .wind-display {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .compass {
            position: relative;
            width: 120px;
            height: 120px;
        }

        .compass-face {
            width: 100%;
            height: 100%;
            border: 3px solid #667eea;
            border-radius: 50%;
            position: relative;
            background: radial-gradient(circle, #f8f9fa 0%, #e5e7eb 100%);
        }

        .compass-needle {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 2px;
            height: 40px;
            background: #ef4444;
            transform-origin: bottom center;
            transition: transform 0.5s ease;
            margin-left: -1px;
            margin-top: -40px;
        }

        .compass-needle::after {
            content: '';
            position: absolute;
            top: -5px;
            left: -3px;
            width: 8px;
            height: 8px;
            background: #ef4444;
            border-radius: 50%;
        }

        .compass-directions {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .compass-directions span {
            position: absolute;
            font-size: 12px;
            font-weight: 600;
            color: #333;
        }

        .north { top: 5px; left: 50%; transform: translateX(-50%); }
        .east { right: 5px; top: 50%; transform: translateY(-50%); }
        .south { bottom: 5px; left: 50%; transform: translateX(-50%); }
        .west { left: 5px; top: 50%; transform: translateY(-50%); }

        .wind-data {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .wind-speed, .wind-direction {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .wind-speed span, .wind-direction span {
            font-size: 12px;
            color: #666;
        }

        .wind-speed strong, .wind-direction strong {
            font-size: 18px;
            color: #333;
        }

        /* Solar gauge now uses standard gauge-card styling */

        /* Enhanced Mobile Responsiveness */

        /* Tablet Styles (768px - 1024px) */
        @media (max-width: 1024px) {
            .power-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
            }

            .water-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .environmental-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Mobile Landscape (481px - 768px) */
        @media (max-width: 768px) {
            .dashboard-content {
                padding: 15px;
            }

            .section-header h2 {
                font-size: 18px;
            }

            .power-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .gauge-card {
                padding: 15px;
            }

            .gauge-header h3 {
                font-size: 12px;
            }

            .gauge-value {
                font-size: 14px;
            }

            .water-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .environmental-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .status-bar {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
                padding: 12px 15px;
            }

            .status-item {
                font-size: 12px;
            }

            .wind-display {
                flex-direction: column;
                align-items: center;
                gap: 15px;
            }

            .compass {
                width: 100px;
                height: 100px;
            }

            .weather-metrics {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .metric {
                padding: 8px;
            }

            .tank-visual {
                width: 60px;
                height: 120px;
            }

            .tank-readings {
                right: -50px;
                font-size: 10px;
            }

            .thermometer {
                height: 120px;
            }

            .thermometer-tube {
                height: 100px;
            }

            .temp-scale {
                height: 100px;
                font-size: 9px;
            }
        }

        /* Mobile Portrait (320px - 480px) */
        @media (max-width: 480px) {
            .sidebar {
                width: 100%;
                transform: translateX(-100%);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .top-nav {
                padding: 10px 15px;
                flex-wrap: wrap;
                gap: 10px;
            }

            .page-title {
                font-size: 18px;
            }

            .search-box {
                display: none;
            }

            .user-menu span {
                display: none;
            }

            .dashboard-content {
                padding: 10px;
            }

            .status-bar {
                padding: 10px;
                margin-bottom: 15px;
            }

            .section-header {
                margin: 20px 0 15px 0;
            }

            .section-header h2 {
                font-size: 16px;
            }

            .power-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .gauge-card {
                padding: 12px;
            }

            .gauge-header {
                margin-bottom: 10px;
            }

            .gauge-header h3 {
                font-size: 11px;
            }

            .unit {
                font-size: 10px;
                padding: 1px 6px;
            }

            .gauge-value {
                font-size: 12px;
            }

            .water-grid {
                gap: 10px;
            }

            .water-level-card, .flow-card, .temp-card {
                padding: 12px;
            }

            .level-header h3, .flow-header h3, .temp-header h3 {
                font-size: 14px;
            }

            .tank-container {
                height: 150px;
            }

            .tank-visual {
                width: 50px;
                height: 100px;
            }

            .tank-readings {
                right: -40px;
                font-size: 9px;
            }

            .flow-meters {
                gap: 10px;
            }

            .flow-meter {
                padding: 8px;
            }

            .flow-icon {
                width: 30px;
                height: 30px;
                font-size: 14px;
            }

            .flow-label {
                font-size: 10px;
            }

            .flow-value {
                font-size: 12px;
            }

            .thermometer {
                height: 100px;
            }

            .thermometer-tube {
                height: 80px;
                width: 6px;
            }

            .thermometer-bulb {
                width: 15px;
                height: 15px;
            }

            .temp-scale {
                height: 80px;
                font-size: 8px;
            }

            .temp-reading {
                font-size: 14px;
            }

            .environmental-grid {
                gap: 10px;
            }

            .weather-card, .wind-card {
                padding: 12px;
            }

            .weather-header h3, .wind-header h3 {
                font-size: 14px;
            }

            .weather-status {
                font-size: 10px;
            }

            .metric {
                padding: 6px;
            }

            .metric i {
                font-size: 14px;
            }

            .metric span {
                font-size: 10px;
            }

            .metric strong {
                font-size: 12px;
            }

            .compass {
                width: 80px;
                height: 80px;
            }

            .compass-needle {
                height: 30px;
                margin-top: -30px;
            }

            .compass-directions span {
                font-size: 10px;
            }

            .wind-speed strong, .wind-direction strong {
                font-size: 14px;
            }

            .wind-speed span, .wind-direction span {
                font-size: 10px;
            }

            /* Solar gauge now uses standard gauge responsive styles */
        }

        /* Extra Small Mobile (max-width: 320px) */
        @media (max-width: 320px) {
            .dashboard-content {
                padding: 8px;
            }

            .status-bar {
                padding: 8px;
            }

            .status-item {
                font-size: 11px;
            }

            .gauge-card {
                padding: 10px;
            }

            .gauge-value {
                font-size: 11px;
            }

            .tank-visual {
                width: 40px;
                height: 80px;
            }

            .tank-readings {
                right: -35px;
                font-size: 8px;
            }

            .flow-icon {
                width: 25px;
                height: 25px;
                font-size: 12px;
            }

            .compass {
                width: 70px;
                height: 70px;
            }

            .compass-needle {
                height: 25px;
                margin-top: -25px;
            }
        }

        /* Touch-friendly improvements */
        @media (hover: none) and (pointer: coarse) {
            .gauge-card, .water-level-card, .flow-card, .temp-card,
            .weather-card, .wind-card {
                transition: none;
            }

            .gauge-card:hover {
                transform: none;
            }

            .menu-item {
                padding: 18px 25px;
                min-height: 44px;
            }

            .menu-toggle {
                padding: 12px;
                min-width: 44px;
                min-height: 44px;
            }

            .user-menu {
                padding: 12px 15px;
                min-height: 44px;
            }

            .status-item {
                padding: 8px 0;
                min-height: 32px;
            }
        }

        /* Landscape orientation adjustments */
        @media (max-width: 768px) and (orientation: landscape) {
            .power-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .water-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .environmental-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .status-bar {
                flex-direction: row;
                flex-wrap: wrap;
            }
        }

        /* High DPI displays */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .gauge-card, .water-level-card, .flow-card, .temp-card,
            .weather-card, .wind-card {
                box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
            }
        }
