<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - CBK</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/login.css') }}">
</head>
<body class="bg-gradient" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center;">

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow-lg p-4 rounded-4">
                    <div class="card-body">
                        <h3 class="card-title text-center mb-3">Welcome Back</h3>
                        <p class="text-center text-muted mb-4">Please sign in to your account</p>

                        {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'danger' else 'success' }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                        {% endwith %}

                        <form method="POST" id="loginForm">
                            {{ form.hidden_tag() }}

                            <div class="mb-3">
                                {{ form.username.label(class="form-label") }}
                                {{ form.username(class="form-control", placeholder="Enter your username") }}
                                {% if form.username.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.username.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                {{ form.password.label(class="form-label") }}
                                {{ form.password(class="form-control", placeholder="Enter your password") }}
                                {% if form.password.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.password.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="d-grid mb-3">
                                {{ form.submit(class="btn btn-primary") }}
                            </div>

                            <div class="text-center">
                                <a href="#" onclick="alert('Password reset not available yet')">Forgot your password?</a>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function () {
            this.classList.add('loading');
        });

        window.addEventListener('load', function () {
            const usernameField = document.querySelector('input[name="username"]');
            if (usernameField) {
                usernameField.focus();
            }
        });
    </script>
</body>
</html>
