from flask import Flask, render_template, request, redirect, flash, url_for, jsonify
from flask_wtf import Flask<PERSON>orm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SubmitField
from wtforms.validators import DataRequired
import random
import time

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'  # Change this to something secure

# WTForms Login Form
class LoginForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    submit = SubmitField('Login')

@app.route('/')
def index():
    """Home page - redirect to login"""
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    form = LoginForm()
    if form.validate_on_submit():
        # Replace with actual authentication logic
        username = form.username.data
        password = form.password.data

        # Simple demo authentication (replace with real authentication)
        if username == "asdf" and password == "asdf":
            flash('Login successful! Welcome back.', 'success')
            # In a real app, you would redirect to a dashboard or user area
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password. Please try again.', 'danger')

    return render_template('login.html', form=form)

@app.route('/dashboard')
def dashboard():
    """Admin Dashboard page"""
    return render_template('admin_dashboard.html')

@app.route('/logout')
def logout():
    """Logout and redirect to login"""
    flash('You have been logged out successfully.', 'success')
    return redirect(url_for('login'))

def get_location_specific_data(location):
    """Get location-specific sensor data with different base values per location"""

    # Define different base values for each location
    location_configs = {
        'botocan': {
            'mw_base': 52.8, 'mw_turbine_base': 55.2, 'mw_pump_base': 2.4,
            'mvar_base': 15.7, 'voltage_base': 14200, 'current_base': 1180,
            'flow_turbine_base': 140.2, 'flow_pump_base': 12.8, 'flow_other_base': 6.5,
            'net_head_base': 92.1, 'temp_water_base': 24.2,
            'upper_water1_base': 72.3, 'upper_water2_base': 71.8,
            'lower_water1_base': 48.7, 'lower_water2_base': 48.2,
            'air_temp_base': 26.8, 'humidity_base': 72, 'pressure_base': 1015.8
        },
        'kalayaan01': {
            'mw_base': 45.2, 'mw_turbine_base': 48.7, 'mw_pump_base': 3.5,
            'mvar_base': 12.3, 'voltage_base': 13800, 'current_base': 1250,
            'flow_turbine_base': 125.5, 'flow_pump_base': 15.2, 'flow_other_base': 8.7,
            'net_head_base': 85.3, 'temp_water_base': 22.5,
            'upper_water1_base': 65.2, 'upper_water2_base': 64.8,
            'lower_water1_base': 45.1, 'lower_water2_base': 44.9,
            'air_temp_base': 28.5, 'humidity_base': 65, 'pressure_base': 1013.2
        },
        'kalayaan02': {
            'mw_base': 38.9, 'mw_turbine_base': 42.1, 'mw_pump_base': 3.2,
            'mvar_base': 10.8, 'voltage_base': 13600, 'current_base': 1150,
            'flow_turbine_base': 118.3, 'flow_pump_base': 14.7, 'flow_other_base': 7.9,
            'net_head_base': 78.6, 'temp_water_base': 23.1,
            'upper_water1_base': 61.7, 'upper_water2_base': 61.3,
            'lower_water1_base': 42.8, 'lower_water2_base': 42.4,
            'air_temp_base': 29.2, 'humidity_base': 68, 'pressure_base': 1012.7
        },
        'kalayaan03': {
            'mw_base': 41.6, 'mw_turbine_base': 44.8, 'mw_pump_base': 3.2,
            'mvar_base': 11.9, 'voltage_base': 13900, 'current_base': 1200,
            'flow_turbine_base': 132.1, 'flow_pump_base': 16.1, 'flow_other_base': 9.2,
            'net_head_base': 88.7, 'temp_water_base': 21.8,
            'upper_water1_base': 67.9, 'upper_water2_base': 67.4,
            'lower_water1_base': 46.3, 'lower_water2_base': 45.8,
            'air_temp_base': 27.9, 'humidity_base': 63, 'pressure_base': 1014.1
        },
        'kalayaan04': {
            'mw_base': 49.3, 'mw_turbine_base': 52.1, 'mw_pump_base': 2.8,
            'mvar_base': 14.2, 'voltage_base': 14100, 'current_base': 1320,
            'flow_turbine_base': 145.8, 'flow_pump_base': 13.4, 'flow_other_base': 8.1,
            'net_head_base': 94.2, 'temp_water_base': 20.9,
            'upper_water1_base': 69.8, 'upper_water2_base': 69.3,
            'lower_water1_base': 49.1, 'lower_water2_base': 48.7,
            'air_temp_base': 26.3, 'humidity_base': 70, 'pressure_base': 1016.3
        }
    }

    # Get configuration for the specified location, default to kalayaan01
    config = location_configs.get(location, location_configs['kalayaan01'])

    # Generate data with location-specific base values and realistic variations
    data = {
        'ts': time.strftime('%Y-%m-%d %H:%M:%S'),
        'location': location,
        'mw': config['mw_base'] + random.uniform(-2, 2),
        'mw_turbine': config['mw_turbine_base'] + random.uniform(-1, 1),
        'mw_pump': config['mw_pump_base'] + random.uniform(-0.5, 0.5),
        'mvar': config['mvar_base'] + random.uniform(-1, 1),
        'voltage': config['voltage_base'] + random.uniform(-200, 200),
        'current': config['current_base'] + random.uniform(-50, 50),
        'hz': 50.0 + random.uniform(-0.1, 0.1),  # Frequency is standard across locations
        'flow_turbine': config['flow_turbine_base'] + random.uniform(-5, 5),
        'flow_pump': config['flow_pump_base'] + random.uniform(-1, 1),
        'flow_other': config['flow_other_base'] + random.uniform(-0.5, 0.5),
        'net_head': config['net_head_base'] + random.uniform(-2, 2),
        'temp_water': config['temp_water_base'] + random.uniform(-1, 1),
        'upper_water1': config['upper_water1_base'] + random.uniform(-1, 1),
        'upper_water2': config['upper_water2_base'] + random.uniform(-1, 1),
        'lower_water1': config['lower_water1_base'] + random.uniform(-1, 1),
        'lower_water2': config['lower_water2_base'] + random.uniform(-1, 1),
        'evaporation': 2.3 + random.uniform(-0.1, 0.1),  # Similar across locations
        'air_temp': config['air_temp_base'] + random.uniform(-2, 2),
        'humidity': config['humidity_base'] + random.uniform(-5, 5),
        'rainfall': max(0, random.uniform(-0.1, 0.5)),  # Weather can vary
        'pressure': config['pressure_base'] + random.uniform(-5, 5),
        'windspeed': 12.5 + random.uniform(-2, 2),  # Weather varies
        'wind_direction': (225 + random.uniform(-30, 30)) % 360,
        'solar_radiation': 850 + random.uniform(-50, 50)
    }

    return data

@app.route('/api/sensor-data')
def get_sensor_data():
    """API endpoint to get current sensor data for specific location"""
    # Get location parameter from request
    location = request.args.get('location', 'kalayaan01')

    # Get location-specific data
    sensor_data = get_location_specific_data(location)

    return jsonify(sensor_data)

if __name__ == '__main__':
    app.run(debug=True)
