from flask import Flask, render_template, request, redirect, flash, url_for
from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SubmitField
from wtforms.validators import DataRequired

app = Flask(__name__)
app.secret_key = 'your-secret-key'  # Change this to something secure

# WTForms Login Form
class LoginForm(FlaskForm):
    username = String<PERSON>ield('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    submit = SubmitField('Login')

@app.route('/login', methods=['GET', 'POST'])
def login():
    form = LoginForm()
    if form.validate_on_submit():
        # Replace with actual authentication logic
        if form.username.data == "admin" and form.password.data == "password":
            flash('Login successful!', 'success')
            return redirect(url_for('login'))  # Change to dashboard
        else:
            flash('Invalid credentials.', 'danger')
    return render_template('login.html', form=form)

if __name__ == '__main__':
    app.run(debug=True)
