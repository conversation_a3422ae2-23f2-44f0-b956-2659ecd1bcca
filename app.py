from flask import Flask, render_template, request, redirect, flash, url_for, jsonify
from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, PasswordField, SubmitField
from wtforms.validators import DataRequired
from flask_mysqldb import MySQL
import random
import time
from db_config import MYSQL_HOST, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'  # Change this to something secure

# MySQL Configuration
app.config['MYSQL_HOST'] = MYSQL_HOST
app.config['MYSQL_USER'] = MYSQL_USER
app.config['MYSQL_PASSWORD'] = MYSQL_PASSWORD
app.config['MYSQL_DB'] = MYSQL_DB
app.config['MYSQL_CURSORCLASS'] = 'DictCursor'

mysql = MySQL(app)

# Location to table mapping
LOCATION_TABLE_MAP = {
    'botocan': 'b01_parameters',
    'kalayaan01': 'k01_parameters',
    'kalayaan02': 'k02_parameters',
    'kalayaan03': 'k03_parameters',
    'kalayaan04': 'k04_parameters'
}

# WTForms Login Form
class LoginForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    submit = SubmitField('Login')

@app.route('/')
def index():
    """Home page - redirect to login"""
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    form = LoginForm()
    if form.validate_on_submit():
        # Replace with actual authentication logic
        username = form.username.data
        password = form.password.data

        # Simple demo authentication (replace with real authentication)
        if username == "asdf" and password == "asdf":
            flash('Login successful! Welcome back.', 'success')
            # In a real app, you would redirect to a dashboard or user area
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password. Please try again.', 'danger')

    return render_template('login.html', form=form)

@app.route('/dashboard')
def dashboard():
    """Admin Dashboard page"""
    return render_template('admin_dashboard.html')

@app.route('/admin_dashboard')
def admin_dashboard():
    """Admin dashboard page (alternative route)"""
    return render_template('admin_dashboard.html')

def get_real_data_from_db(location):
    """Fetch real data from database based on location"""
    try:
        # Get the table name for the location
        table_name = LOCATION_TABLE_MAP.get(location)
        if not table_name:
            return None

        cur = mysql.connection.cursor()

        # Query to get the latest record from the location's table
        query = f"""
        SELECT * FROM {table_name}
        ORDER BY ts DESC
        LIMIT 1
        """

        cur.execute(query)
        result = cur.fetchone()
        cur.close()

        if result:
            # Map database columns to our expected format based on your actual columns
            data = {
                'ts': result.get('ts', time.strftime('%Y-%m-%d %H:%M:%S')),
                'location': location,
                'mw': result.get('mw', 0),  # Total power
                'mw_turbine': result.get('mw_turbine', 0),  # Turbine output
                'mw_pump': result.get('mw_pump', 0),  # Pump consumption
                'mvar': result.get('mvar', 0),  # MVAR from your table
                'voltage': result.get('voltage', 0),  # Voltage from your table
                'current': result.get('current', 0),  # Current from your table
                'hz': result.get('hz', 50.0),  # Frequency from your table
                'flow_turbine': result.get('flow_turbine', 0),  # Flow turbine from your table
                'flow_pump': result.get('flow_pump', 0),  # Flow pump from your table
                'flow_other': result.get('flow_other', 0),  # Flow other from your table
                'net_head': result.get('net_head', 0),  # Net head from your table
                'temp_water': result.get('temp_water', 0),  # Water temperature from your table
                'upper_water1': result.get('upper_water', 0),  # Upper water from your table
                'upper_water2': result.get('upper_water', 0),  # Using same value for both
                'lower_water1': result.get('lower_water', 0),  # If you have lower_water column
                'lower_water2': result.get('lower_water', 0),  # Using same value for both
                'evaporation': result.get('evaporation', 0),  # Evaporation from your table
                'air_temp': result.get('t', 0),  # Temperature (t column) from your table
                'humidity': result.get('rh', 0),  # Relative humidity (rh column) from your table
                'rainfall': result.get('rr', 0),  # Rainfall (rr column) from your table
                'pressure': result.get('sp', 0),  # Surface pressure (sp column) from your table
                'windspeed': result.get('ws', 0),  # Wind speed (ws column) from your table
                'wind_direction': result.get('wd', 0),  # Wind direction (wd column) from your table
                'solar_radiation': result.get('sr', 0),  # Solar radiation (sr column) from your table
                'data_source': 'database'
            }
            return data
        else:
            return None

    except Exception as e:
        print(f"Database error for location {location}: {e}")
        return None

@app.route('/logout')
def logout():
    """Logout and redirect to login"""
    flash('You have been logged out successfully.', 'success')
    return redirect(url_for('login'))

def get_location_specific_data(location):
    """Get location-specific sensor data with different base values per location"""

    # Define different base values for each location
    location_configs = {
        'botocan': {
            'mw_base': 52.8, 'mw_turbine_base': 55.2, 'mw_pump_base': 2.4,
            'mvar_base': 15.7, 'voltage_base': 14200, 'current_base': 1180,
            'flow_turbine_base': 140.2, 'flow_pump_base': 12.8, 'flow_other_base': 6.5,
            'net_head_base': 92.1, 'temp_water_base': 24.2,
            'upper_water1_base': 72.3, 'upper_water2_base': 71.8,
            'lower_water1_base': 48.7, 'lower_water2_base': 48.2,
            'air_temp_base': 26.8, 'humidity_base': 72, 'pressure_base': 1015.8
        },
        'kalayaan01': {
            'mw_base': 45.2, 'mw_turbine_base': 48.7, 'mw_pump_base': 3.5,
            'mvar_base': 12.3, 'voltage_base': 13800, 'current_base': 1250,
            'flow_turbine_base': 125.5, 'flow_pump_base': 15.2, 'flow_other_base': 8.7,
            'net_head_base': 85.3, 'temp_water_base': 22.5,
            'upper_water1_base': 65.2, 'upper_water2_base': 64.8,
            'lower_water1_base': 45.1, 'lower_water2_base': 44.9,
            'air_temp_base': 28.5, 'humidity_base': 65, 'pressure_base': 1013.2
        },
        'kalayaan02': {
            'mw_base': 38.9, 'mw_turbine_base': 42.1, 'mw_pump_base': 3.2,
            'mvar_base': 10.8, 'voltage_base': 13600, 'current_base': 1150,
            'flow_turbine_base': 118.3, 'flow_pump_base': 14.7, 'flow_other_base': 7.9,
            'net_head_base': 78.6, 'temp_water_base': 23.1,
            'upper_water1_base': 61.7, 'upper_water2_base': 61.3,
            'lower_water1_base': 42.8, 'lower_water2_base': 42.4,
            'air_temp_base': 29.2, 'humidity_base': 68, 'pressure_base': 1012.7
        },
        'kalayaan03': {
            'mw_base': 41.6, 'mw_turbine_base': 44.8, 'mw_pump_base': 3.2,
            'mvar_base': 11.9, 'voltage_base': 13900, 'current_base': 1200,
            'flow_turbine_base': 132.1, 'flow_pump_base': 16.1, 'flow_other_base': 9.2,
            'net_head_base': 88.7, 'temp_water_base': 21.8,
            'upper_water1_base': 67.9, 'upper_water2_base': 67.4,
            'lower_water1_base': 46.3, 'lower_water2_base': 45.8,
            'air_temp_base': 27.9, 'humidity_base': 63, 'pressure_base': 1014.1
        },
        'kalayaan04': {
            'mw_base': 49.3, 'mw_turbine_base': 52.1, 'mw_pump_base': 2.8,
            'mvar_base': 14.2, 'voltage_base': 14100, 'current_base': 1320,
            'flow_turbine_base': 145.8, 'flow_pump_base': 13.4, 'flow_other_base': 8.1,
            'net_head_base': 94.2, 'temp_water_base': 20.9,
            'upper_water1_base': 69.8, 'upper_water2_base': 69.3,
            'lower_water1_base': 49.1, 'lower_water2_base': 48.7,
            'air_temp_base': 26.3, 'humidity_base': 70, 'pressure_base': 1016.3
        }
    }

    # Get configuration for the specified location, default to kalayaan01
    config = location_configs.get(location, location_configs['kalayaan01'])

    # Generate data with location-specific base values and realistic variations
    data = {
        'ts': time.strftime('%Y-%m-%d %H:%M:%S'),
        'location': location,
        'mw': config['mw_base'] + random.uniform(-2, 2),
        'mw_turbine': config['mw_turbine_base'] + random.uniform(-1, 1),
        'mw_pump': config['mw_pump_base'] + random.uniform(-0.5, 0.5),
        'mvar': config['mvar_base'] + random.uniform(-1, 1),
        'voltage': config['voltage_base'] + random.uniform(-200, 200),
        'current': config['current_base'] + random.uniform(-50, 50),
        'hz': 50.0 + random.uniform(-0.1, 0.1),  # Frequency is standard across locations
        'flow_turbine': config['flow_turbine_base'] + random.uniform(-5, 5),
        'flow_pump': config['flow_pump_base'] + random.uniform(-1, 1),
        'flow_other': config['flow_other_base'] + random.uniform(-0.5, 0.5),
        'net_head': config['net_head_base'] + random.uniform(-2, 2),
        'temp_water': config['temp_water_base'] + random.uniform(-1, 1),
        'upper_water1': config['upper_water1_base'] + random.uniform(-1, 1),
        'upper_water2': config['upper_water2_base'] + random.uniform(-1, 1),
        'lower_water1': config['lower_water1_base'] + random.uniform(-1, 1),
        'lower_water2': config['lower_water2_base'] + random.uniform(-1, 1),
        'evaporation': 2.3 + random.uniform(-0.1, 0.1),  # Similar across locations
        'air_temp': config['air_temp_base'] + random.uniform(-2, 2),
        'humidity': config['humidity_base'] + random.uniform(-5, 5),
        'rainfall': max(0, random.uniform(-0.1, 0.5)),  # Weather can vary
        'pressure': config['pressure_base'] + random.uniform(-5, 5),
        'windspeed': 12.5 + random.uniform(-2, 2),  # Weather varies
        'wind_direction': (225 + random.uniform(-30, 30)) % 360,
        'solar_radiation': 850 + random.uniform(-50, 50)
    }

    return data

@app.route('/api/sensor-data')
def get_sensor_data():
    """API endpoint to get current sensor data for specific location"""
    # Get location parameter from request
    location = request.args.get('location', 'kalayaan01')

    # Handle special cases
    if location == 'all':
        # Return aggregated data from all locations
        return jsonify(get_aggregated_data())
    elif location == 'none':
        # Return empty/null data
        return jsonify(get_empty_data())

    # Try to get real data from database first
    real_data = get_real_data_from_db(location)

    if real_data:
        # Use real database data
        sensor_data = real_data
    else:
        # Fallback to simulated data if database is unavailable
        sensor_data = get_location_specific_data(location)
        # Add a flag to indicate this is simulated data
        sensor_data['data_source'] = 'simulated'

    return jsonify(sensor_data)

def get_aggregated_data():
    """Get aggregated data from all locations"""
    all_locations = ['botocan', 'kalayaan01', 'kalayaan02', 'kalayaan03', 'kalayaan04']
    total_data = {
        'ts': time.strftime('%Y-%m-%d %H:%M:%S'),
        'location': 'all',
        'mw': 0, 'mw_turbine': 0, 'mw_pump': 0, 'mvar': 0,
        'voltage': 0, 'current': 0, 'hz': 0,
        'flow_turbine': 0, 'flow_pump': 0, 'flow_other': 0,
        'net_head': 0, 'temp_water': 0,
        'upper_water1': 0, 'upper_water2': 0,
        'lower_water1': 0, 'lower_water2': 0,
        'evaporation': 0, 'air_temp': 0, 'humidity': 0,
        'rainfall': 0, 'pressure': 0, 'windspeed': 0,
        'wind_direction': 0, 'solar_radiation': 0,
        'data_source': 'aggregated'
    }

    valid_locations = 0
    for loc in all_locations:
        data = get_real_data_from_db(loc)
        if not data:
            data = get_location_specific_data(loc)

        if data:
            valid_locations += 1
            # Sum power values
            total_data['mw'] += data.get('mw', 0)
            total_data['mw_turbine'] += data.get('mw_turbine', 0)
            total_data['mw_pump'] += data.get('mw_pump', 0)
            total_data['mvar'] += data.get('mvar', 0)

            # Average other values
            total_data['voltage'] += data.get('voltage', 0)
            total_data['current'] += data.get('current', 0)
            total_data['hz'] += data.get('hz', 0)
            total_data['net_head'] += data.get('net_head', 0)
            total_data['temp_water'] += data.get('temp_water', 0)
            total_data['air_temp'] += data.get('air_temp', 0)
            total_data['humidity'] += data.get('humidity', 0)
            total_data['pressure'] += data.get('pressure', 0)

    # Calculate averages for non-additive values
    if valid_locations > 0:
        for key in ['voltage', 'current', 'hz', 'net_head', 'temp_water', 'air_temp', 'humidity', 'pressure']:
            total_data[key] = total_data[key] / valid_locations

    return total_data

def get_empty_data():
    """Return empty data structure for 'none' selection"""
    return {
        'ts': time.strftime('%Y-%m-%d %H:%M:%S'),
        'location': 'none',
        'mw': 0, 'mw_turbine': 0, 'mw_pump': 0, 'mvar': 0,
        'voltage': 0, 'current': 0, 'hz': 0,
        'flow_turbine': 0, 'flow_pump': 0, 'flow_other': 0,
        'net_head': 0, 'temp_water': 0,
        'upper_water1': 0, 'upper_water2': 0,
        'lower_water1': 0, 'lower_water2': 0,
        'evaporation': 0, 'air_temp': 0, 'humidity': 0,
        'rainfall': 0, 'pressure': 0, 'windspeed': 0,
        'wind_direction': 0, 'solar_radiation': 0,
        'data_source': 'none'
    }

if __name__ == '__main__':
    app.run(debug=True)
