from flask import Flask, render_template, request, redirect, flash, url_for, jsonify
from flask_wtf import Flask<PERSON>orm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SubmitField
from wtforms.validators import DataRequired
import random
import time

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'  # Change this to something secure

# WTForms Login Form
class LoginForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    submit = SubmitField('Login')

@app.route('/')
def index():
    """Home page - redirect to login"""
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    form = LoginForm()
    if form.validate_on_submit():
        # Replace with actual authentication logic
        username = form.username.data
        password = form.password.data

        # Simple demo authentication (replace with real authentication)
        if username == "asdf" and password == "asdf":
            flash('Login successful! Welcome back.', 'success')
            # In a real app, you would redirect to a dashboard or user area
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password. Please try again.', 'danger')

    return render_template('login.html', form=form)

@app.route('/dashboard')
def dashboard():
    """Admin Dashboard page"""
    return render_template('admin_dashboard.html')

@app.route('/logout')
def logout():
    """Logout and redirect to login"""
    flash('You have been logged out successfully.', 'success')
    return redirect(url_for('login'))

@app.route('/api/sensor-data')
def get_sensor_data():
    """API endpoint to get current sensor data"""
    # Get location parameter (for future use)
    location = request.args.get('location', 'kalayaan01')

    # TODO: In the future, you can use the location parameter to fetch
    # location-specific data from your database

    # Simulate real sensor data with some randomization
    # You can modify these base values per location in the future
    base_data = {
        'ts': time.strftime('%Y-%m-%d %H:%M:%S'),
        'mw': 45.2 + random.uniform(-2, 2),
        'mw_turbine': 48.7 + random.uniform(-1, 1),
        'mw_pump': 3.5 + random.uniform(-0.5, 0.5),
        'mvar': 12.3 + random.uniform(-1, 1),
        'voltage': 13800 + random.uniform(-200, 200),
        'current': 1250 + random.uniform(-50, 50),
        'hz': 50.0 + random.uniform(-0.1, 0.1),
        'flow_turbine': 125.5 + random.uniform(-5, 5),
        'flow_pump': 15.2 + random.uniform(-1, 1),
        'flow_other': 8.7 + random.uniform(-0.5, 0.5),
        'net_head': 85.3 + random.uniform(-2, 2),
        'temp_water': 22.5 + random.uniform(-1, 1),
        'upper_water1': 65.2 + random.uniform(-1, 1),
        'upper_water2': 64.8 + random.uniform(-1, 1),
        'lower_water1': 45.1 + random.uniform(-1, 1),
        'lower_water2': 44.9 + random.uniform(-1, 1),
        'evaporation': 2.3 + random.uniform(-0.1, 0.1),
        'air_temp': 28.5 + random.uniform(-2, 2),
        'humidity': 65 + random.uniform(-5, 5),
        'rainfall': max(0, random.uniform(-0.1, 0.5)),
        'pressure': 1013.2 + random.uniform(-5, 5),
        'windspeed': 12.5 + random.uniform(-2, 2),
        'wind_direction': (225 + random.uniform(-30, 30)) % 360,
        'solar_radiation': 850 + random.uniform(-50, 50)
    }

    return jsonify(base_data)

if __name__ == '__main__':
    app.run(debug=True)
